"""
FastLLaMA Web UI for Inference

A comprehensive web interface for FastLLaMA model inference with:
- Dynamic model loading from any path
- Configurable generation parameters
- Debug information display
- Real-time inference
- Model comparison capabilities
"""

import os
import sys
import torch
import gradio as gr
import json
import time
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLa<PERSON>Config, FastLLaMAModel
from transformers import AutoTokenizer

class FastLLaMAInferenceEngine:
    """FastLLaMA inference engine with web UI support."""

    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.config = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model_info = {}

    def load_model(self, model_path: str) -> Tuple[bool, str]:
        """Load FastLLaMA model from path."""
        try:
            model_path = Path(model_path)

            # Check if path exists
            if not model_path.exists():
                return False, f"❌ Path does not exist: {model_path}"

            # Load config
            config_path = model_path / "config.json"
            if config_path.exists():
                with open(config_path, 'r') as f:
                    config_dict = json.load(f)
                self.config = FastLLaMAConfig(**config_dict)
            else:
                return False, f"❌ Config file not found: {config_path}"

            # Load tokenizer
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(model_path)
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token
            except Exception as e:
                return False, f"❌ Failed to load tokenizer: {e}"

            # Load model
            self.model = FastLLaMAModel(self.config)

            # Load weights
            weights_path = model_path / "pytorch_model.bin"
            if weights_path.exists():
                state_dict = torch.load(weights_path, map_location=self.device)
                self.model.load_state_dict(state_dict)
            else:
                return False, f"❌ Model weights not found: {weights_path}"

            self.model.to(self.device)
            self.model.eval()

            # Store model info
            self.model_info = {
                "path": str(model_path),
                "parameters": sum(p.numel() for p in self.model.parameters()),
                "trainable_parameters": sum(p.numel() for p in self.model.parameters() if p.requires_grad),
                "vocab_size": self.tokenizer.vocab_size,
                "hidden_size": self.config.hidden_size,
                "num_layers": self.config.num_hidden_layers,
                "num_attention_heads": self.config.num_attention_heads,
                "device": str(self.device),
                "features": {
                    "context_compression": getattr(self.config, 'enable_context_compression', False),
                    "early_exit": getattr(self.config, 'enable_early_exit', False),
                    "gqa": self.config.num_key_value_heads < self.config.num_attention_heads,
                    "parameter_sharing": getattr(self.config, 'parameter_sharing', False),
                    "kv_cache_quantization": getattr(self.config, 'kv_cache_quantization', False),
                }
            }

            return True, f"✅ Model loaded successfully from {model_path}"

        except Exception as e:
            return False, f"❌ Failed to load model: {e}\n{traceback.format_exc()}"

    def generate_text(
        self,
        prompt: str,
        max_new_tokens: int = 100,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
        repetition_penalty: float = 1.1,
        do_sample: bool = True,
        show_debug: bool = False
    ) -> Tuple[str, Dict[str, Any]]:
        """Generate text with the loaded model."""
        if self.model is None or self.tokenizer is None:
            return "❌ No model loaded", {}

        try:
            start_time = time.time()

            # Tokenize input
            inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
            input_ids = inputs.input_ids
            input_length = input_ids.size(1)

            # Generation loop
            generated_ids = input_ids.clone()
            generation_stats = {
                "tokens_generated": 0,
                "generation_time": 0,
                "tokens_per_second": 0,
                "input_tokens": input_length,
                "total_tokens": input_length,
                "memory_usage": {},
                "attention_info": [],
                "early_exit_info": []
            }

            with torch.no_grad():
                for step in range(max_new_tokens):
                    # Forward pass
                    outputs = self.model(input_ids=generated_ids)
                    logits = outputs['logits']

                    # Get next token logits
                    next_token_logits = logits[0, -1, :]

                    # Apply temperature
                    if temperature != 1.0:
                        next_token_logits = next_token_logits / temperature

                    # Apply repetition penalty
                    if repetition_penalty != 1.0:
                        for token_id in set(generated_ids[0].tolist()):
                            next_token_logits[token_id] /= repetition_penalty

                    # Apply top-k filtering
                    if top_k > 0:
                        top_k_logits, top_k_indices = torch.topk(next_token_logits, top_k)
                        next_token_logits = torch.full_like(next_token_logits, float('-inf'))
                        next_token_logits[top_k_indices] = top_k_logits

                    # Apply top-p filtering
                    if top_p < 1.0:
                        sorted_logits, sorted_indices = torch.sort(next_token_logits, descending=True)
                        cumulative_probs = torch.cumsum(torch.softmax(sorted_logits, dim=-1), dim=-1)
                        sorted_indices_to_remove = cumulative_probs > top_p
                        sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
                        sorted_indices_to_remove[0] = 0
                        indices_to_remove = sorted_indices[sorted_indices_to_remove]
                        next_token_logits[indices_to_remove] = float('-inf')

                    # Sample next token
                    if do_sample:
                        probs = torch.softmax(next_token_logits, dim=-1)
                        next_token_id = torch.multinomial(probs, num_samples=1)
                    else:
                        next_token_id = torch.argmax(next_token_logits, dim=-1).unsqueeze(0)

                    # Stop if EOS token
                    if next_token_id.item() == self.tokenizer.eos_token_id:
                        break

                    # Append to sequence
                    generated_ids = torch.cat([generated_ids, next_token_id.unsqueeze(0)], dim=1)
                    generation_stats["tokens_generated"] += 1

                    # Collect debug info
                    if show_debug:
                        token_text = self.tokenizer.decode(next_token_id.item())
                        top_5_probs = torch.softmax(next_token_logits, dim=-1).topk(5)
                        generation_stats["attention_info"].append({
                            "step": step,
                            "token": token_text,
                            "token_id": next_token_id.item(),
                            "top_5_tokens": [
                                (self.tokenizer.decode(idx.item()), prob.item())
                                for idx, prob in zip(top_5_probs.indices, top_5_probs.values)
                            ]
                        })

            # Decode generated text
            generated_text = self.tokenizer.decode(generated_ids[0], skip_special_tokens=True)

            # Calculate stats
            end_time = time.time()
            generation_stats["generation_time"] = end_time - start_time
            generation_stats["total_tokens"] = generated_ids.size(1)
            generation_stats["tokens_per_second"] = generation_stats["tokens_generated"] / generation_stats["generation_time"] if generation_stats["generation_time"] > 0 else 0

            # Memory usage
            if torch.cuda.is_available():
                generation_stats["memory_usage"] = {
                    "allocated_gb": torch.cuda.memory_allocated() / 1e9,
                    "reserved_gb": torch.cuda.memory_reserved() / 1e9,
                }

            return generated_text, generation_stats

        except Exception as e:
            return f"❌ Generation failed: {e}\n{traceback.format_exc()}", {}

# Global inference engine
inference_engine = FastLLaMAInferenceEngine()

def load_model_ui(model_path: str) -> Tuple[str, str]:
    """UI function to load model."""
    success, message = inference_engine.load_model(model_path)

    if success:
        info = inference_engine.model_info
        model_info = f"""
## 📊 Model Information

**Path:** `{info['path']}`
**Parameters:** {info['parameters']:,} ({info['parameters']/1e6:.1f}M)
**Trainable:** {info['trainable_parameters']:,} ({info['trainable_parameters']/1e6:.1f}M)
**Vocabulary Size:** {info['vocab_size']:,}
**Hidden Size:** {info['hidden_size']}
**Layers:** {info['num_layers']}
**Attention Heads:** {info['num_attention_heads']}
**Device:** {info['device']}

### 🔧 Features
- **Context Compression:** {'✅' if info['features']['context_compression'] else '❌'}
- **Early Exit:** {'✅' if info['features']['early_exit'] else '❌'}
- **Grouped Query Attention:** {'✅' if info['features']['gqa'] else '❌'}
- **Parameter Sharing:** {'✅' if info['features']['parameter_sharing'] else '❌'}
- **KV Cache Quantization:** {'✅' if info['features']['kv_cache_quantization'] else '❌'}
"""
        return message, model_info
    else:
        return message, "❌ No model loaded"

def generate_ui(
    prompt: str,
    max_new_tokens: int,
    temperature: float,
    top_p: float,
    top_k: int,
    repetition_penalty: float,
    do_sample: bool,
    show_debug: bool
) -> Tuple[str, str]:
    """UI function to generate text."""
    if not prompt.strip():
        return "❌ Please enter a prompt", ""

    generated_text, stats = inference_engine.generate_text(
        prompt=prompt,
        max_new_tokens=max_new_tokens,
        temperature=temperature,
        top_p=top_p,
        top_k=top_k,
        repetition_penalty=repetition_penalty,
        do_sample=do_sample,
        show_debug=show_debug
    )

    # Format debug info
    debug_info = ""
    if show_debug and stats:
        debug_info = f"""
## 📈 Generation Statistics

**Tokens Generated:** {stats.get('tokens_generated', 0)}
**Generation Time:** {stats.get('generation_time', 0):.2f}s
**Speed:** {stats.get('tokens_per_second', 0):.1f} tokens/sec
**Input Tokens:** {stats.get('input_tokens', 0)}
**Total Tokens:** {stats.get('total_tokens', 0)}

### 💾 Memory Usage
**GPU Allocated:** {stats.get('memory_usage', {}).get('allocated_gb', 0):.2f} GB
**GPU Reserved:** {stats.get('memory_usage', {}).get('reserved_gb', 0):.2f} GB

### 🔍 Token Generation Details
"""
        for i, info in enumerate(stats.get('attention_info', [])[:10]):  # Show first 10 steps
            debug_info += f"\n**Step {info['step']+1}:** `{info['token']}` (ID: {info['token_id']})\n"
            debug_info += "Top 5 candidates:\n"
            for token, prob in info['top_5_tokens']:
                debug_info += f"- `{token}`: {prob:.3f}\n"

    return generated_text, debug_info

def create_web_ui():
    """Create the Gradio web interface."""

    with gr.Blocks(
        title="FastLLaMA Web UI",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .model-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        """
    ) as demo:

        gr.Markdown("""
        # 🚀 FastLLaMA Web UI

        **Dynamic FastLLaMA Model Inference Interface**

        Load any FastLLaMA model from a local path and generate text with configurable parameters.
        """)

        with gr.Row():
            with gr.Column(scale=2):
                # Model Loading Section
                gr.Markdown("## 📂 Model Loading")

                model_path_input = gr.Textbox(
                    label="Model Path",
                    placeholder="Enter path to FastLLaMA model directory (e.g., ./fastllama_fixed_output/fixed_model)",
                    value="./fastllama_fixed_output/fixed_model",
                    lines=1
                )

                load_btn = gr.Button("🔄 Load Model", variant="primary")
                load_status = gr.Textbox(label="Status", interactive=False)

                # Text Generation Section
                gr.Markdown("## ✍️ Text Generation")

                prompt_input = gr.Textbox(
                    label="Prompt",
                    placeholder="Enter your prompt here...",
                    lines=3,
                    value="The future of artificial intelligence is"
                )

                generate_btn = gr.Button("🎯 Generate Text", variant="primary")

                # Generation Parameters
                with gr.Accordion("⚙️ Generation Parameters", open=False):
                    with gr.Row():
                        max_new_tokens = gr.Slider(
                            minimum=1, maximum=500, value=100, step=1,
                            label="Max New Tokens"
                        )
                        temperature = gr.Slider(
                            minimum=0.1, maximum=2.0, value=0.7, step=0.1,
                            label="Temperature"
                        )

                    with gr.Row():
                        top_p = gr.Slider(
                            minimum=0.1, maximum=1.0, value=0.9, step=0.05,
                            label="Top-p (Nucleus Sampling)"
                        )
                        top_k = gr.Slider(
                            minimum=1, maximum=100, value=50, step=1,
                            label="Top-k"
                        )

                    with gr.Row():
                        repetition_penalty = gr.Slider(
                            minimum=1.0, maximum=2.0, value=1.1, step=0.05,
                            label="Repetition Penalty"
                        )
                        do_sample = gr.Checkbox(
                            value=True,
                            label="Do Sample (vs Greedy)"
                        )

                    show_debug = gr.Checkbox(
                        value=False,
                        label="Show Debug Information"
                    )

            with gr.Column(scale=3):
                # Model Information
                gr.Markdown("## 📊 Model Information")
                model_info_display = gr.Markdown(
                    "❌ No model loaded",
                    elem_classes=["model-info"]
                )

                # Generated Text Output
                gr.Markdown("## 📝 Generated Text")
                generated_output = gr.Textbox(
                    label="Generated Text",
                    lines=10,
                    interactive=False,
                    placeholder="Generated text will appear here..."
                )

                # Debug Information
                with gr.Accordion("🔍 Debug Information", open=False):
                    debug_output = gr.Markdown("No debug information available")

        # Examples Section
        gr.Markdown("## 💡 Example Prompts")

        example_prompts = [
            "The future of artificial intelligence is",
            "In a world where technology advances rapidly,",
            "The most important lesson I learned was",
            "Once upon a time in a distant galaxy,",
            "The key to solving climate change lies in",
            "Explain quantum computing in simple terms:",
            "Write a short story about a robot who",
            "The benefits of renewable energy include"
        ]

        gr.Examples(
            examples=[[prompt] for prompt in example_prompts],
            inputs=[prompt_input],
            label="Click to use example prompts"
        )

        # Event handlers
        load_btn.click(
            fn=load_model_ui,
            inputs=[model_path_input],
            outputs=[load_status, model_info_display]
        )

        generate_btn.click(
            fn=generate_ui,
            inputs=[
                prompt_input,
                max_new_tokens,
                temperature,
                top_p,
                top_k,
                repetition_penalty,
                do_sample,
                show_debug
            ],
            outputs=[generated_output, debug_output]
        )

        # Footer
        gr.Markdown("""
        ---
        **FastLLaMA Web UI** - Dynamic model loading and inference interface

        🔧 **Features:**
        - Load any FastLLaMA model from local path
        - Configurable generation parameters
        - Real-time debug information
        - Memory usage monitoring
        - Token-level generation details
        """)

    return demo

if __name__ == "__main__":
    # Create and launch the web UI
    demo = create_web_ui()

    print("🚀 Starting FastLLaMA Web UI...")
    print("📂 Default model path: ./fastllama_fixed_output/fixed_model")
    print("🌐 Open your browser to interact with the model")

    demo.launch(
        server_name="0.0.0.0",  # Allow external access
        server_port=7860,       # Default Gradio port
        share=False,            # Set to True to create public link
        debug=True,             # Enable debug mode
        show_error=True         # Show detailed error messages
    )
