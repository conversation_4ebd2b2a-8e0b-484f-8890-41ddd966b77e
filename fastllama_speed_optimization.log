2025-05-30 21:48:39,531 - INFO - Using device: cuda
2025-05-30 21:48:39,532 - INFO - GPU: NVIDIA GeForce RTX 4070
2025-05-30 21:48:39,532 - INFO - GPU Memory: 12.9GB
2025-05-30 21:48:43,719 - INFO - Initial memory usage: {'gpu_allocated_gb': 1.2524456977844238, 'gpu_reserved_gb': 1.28125, 'gpu_max_allocated_gb': 1.2524456977844238, 'gpu_max_reserved_gb': 1.28125, 'cpu_rss_gb': 2.0301551818847656, 'cpu_vms_gb': 5.079067230224609, 'cpu_percent': 6.3802402248386905, 'system_total_gb': 31.819416046142578, 'system_available_gb': 10.082378387451172, 'system_used_percent': 68.3}
2025-05-30 21:48:43,722 - INFO - Starting FastLLaMA training...
2025-05-30 21:48:43,722 - INFO - Starting foundation phase...
2025-05-30 21:48:50,649 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 21:48:50,649 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 22:00:53,869 - INFO - 🚀 FastLLaMA Training Speed Optimization
2025-05-30 22:00:53,869 - INFO - 🎯 Target: Increase speed from 1.1 it/s to 3-5 it/s
2025-05-30 22:00:53,869 - INFO - Using device: cuda
2025-05-30 22:00:53,870 - INFO - GPU: NVIDIA GeForce RTX 4070
2025-05-30 22:00:53,870 - INFO - GPU Memory: 12.9GB
2025-05-30 22:00:53,870 - INFO - 📚 Creating speed-optimized data configuration...
2025-05-30 22:00:53,870 - INFO - ✅ Batch size increased: 2 → 8
2025-05-30 22:00:53,870 - INFO - ✅ Num workers increased: 2 → 4
2025-05-30 22:00:54,010 - INFO - ✅ Tokenizer loaded. Vocab size: 64811
2025-05-30 22:00:54,010 - INFO - 🧠 Creating speed-optimized FastLLaMA model...
2025-05-30 22:00:56,839 - INFO - 🚀 Applying speed optimizations...
2025-05-30 22:00:56,839 - INFO - 📦 Compiling model with torch.compile...
2025-05-30 22:00:57,486 - INFO - ✅ Model compiled successfully
2025-05-30 22:00:57,486 - INFO - ✅ Speed optimizations applied
2025-05-30 22:00:57,868 - INFO - ✅ Model created with 333.3M total parameters
2025-05-30 22:00:57,868 - INFO - ✅ Trainable parameters: 333.3M
2025-05-30 22:00:57,868 - INFO - ⚙️ Setting up speed-optimized training configuration...
2025-05-30 22:00:57,868 - INFO - ✅ Effective batch size: 8
2025-05-30 22:00:57,868 - INFO - 🏋️ Creating optimized trainer...
2025-05-30 22:00:57,879 - INFO - Initial memory usage: {'gpu_allocated_gb': 1.2524456977844238, 'gpu_reserved_gb': 1.28125, 'gpu_max_allocated_gb': 1.2524456977844238, 'gpu_max_reserved_gb': 1.28125, 'cpu_rss_gb': 2.0326156616210938, 'cpu_vms_gb': 5.0791015625, 'cpu_percent': 6.387972861203734, 'system_total_gb': 31.819416046142578, 'system_available_gb': 10.941963195800781, 'system_used_percent': 65.6}
2025-05-30 22:00:57,879 - INFO - 📊 Benchmarking training speed...
2025-05-30 22:00:57,879 - INFO - 🔥 Warming up...
2025-05-30 22:00:57,879 - INFO - ✅ Warmup completed in 0.00s
2025-05-30 22:00:57,879 - INFO - ⏱️ Starting speed benchmark...
2025-05-30 22:00:57,879 - INFO - Starting FastLLaMA training...
2025-05-30 22:00:57,881 - INFO - Starting foundation phase...
2025-05-30 22:01:07,427 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 22:01:07,427 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 22:07:26,388 - ERROR - ❌ Benchmark failed: CalledProcessError: Command '['D:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\runtime\\tcc\\tcc.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp013lpc4r\\__triton_launcher.c', '-O3', '-shared', '-fPIC', '-Wno-psabi', '-o', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp013lpc4r\\__triton_launcher.cp312-win_amd64.pyd', '-lcuda', '-lpython3', '-LD:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\lib', '-LC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\lib\\x64', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-ID:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\include', '-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\include', '-IC:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp013lpc4r', '-ID:\\.conda\\envs\\tts\\Include']' returned non-zero exit status 1.
2025-05-30 22:07:26,388 - ERROR - ❌ Speed optimization failed: CalledProcessError: Command '['D:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\runtime\\tcc\\tcc.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp013lpc4r\\__triton_launcher.c', '-O3', '-shared', '-fPIC', '-Wno-psabi', '-o', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp013lpc4r\\__triton_launcher.cp312-win_amd64.pyd', '-lcuda', '-lpython3', '-LD:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\lib', '-LC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\lib\\x64', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-LD:\\.conda\\envs\\tts\\libs', '-ID:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\include', '-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\include', '-IC:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp013lpc4r', '-ID:\\.conda\\envs\\tts\\Include']' returned non-zero exit status 1.
2025-05-30 22:08:01,373 - INFO - 🚀 FastLLaMA Training Speed Optimization
2025-05-30 22:08:01,373 - INFO - 🎯 Target: Increase speed from 1.1 it/s to 3-5 it/s
2025-05-30 22:08:01,374 - INFO - Using device: cuda
2025-05-30 22:08:01,374 - INFO - GPU: NVIDIA GeForce RTX 4070
2025-05-30 22:08:01,374 - INFO - GPU Memory: 12.9GB
2025-05-30 22:08:01,374 - INFO - 📚 Creating speed-optimized data configuration...
2025-05-30 22:08:01,374 - INFO - ✅ Batch size increased: 2 → 8
2025-05-30 22:08:01,375 - INFO - ✅ Num workers increased: 2 → 4
2025-05-30 22:08:01,518 - INFO - ✅ Tokenizer loaded. Vocab size: 64811
2025-05-30 22:08:01,518 - INFO - 🧠 Creating speed-optimized FastLLaMA model...
2025-05-30 22:08:04,356 - INFO - 🚀 Applying speed optimizations...
2025-05-30 22:08:04,356 - INFO - 📦 Compiling model with torch.compile...
2025-05-30 22:08:05,022 - INFO - ✅ Model compiled successfully
2025-05-30 22:08:05,023 - INFO - ✅ Speed optimizations applied
2025-05-30 22:08:05,409 - INFO - ✅ Model created with 333.3M total parameters
2025-05-30 22:08:05,409 - INFO - ✅ Trainable parameters: 333.3M
2025-05-30 22:08:05,409 - INFO - ⚙️ Setting up speed-optimized training configuration...
2025-05-30 22:08:05,409 - INFO - ✅ Effective batch size: 8
2025-05-30 22:08:05,409 - INFO - 🏋️ Creating optimized trainer...
2025-05-30 22:08:05,422 - INFO - Initial memory usage: {'gpu_allocated_gb': 1.2524456977844238, 'gpu_reserved_gb': 1.28125, 'gpu_max_allocated_gb': 1.2524456977844238, 'gpu_max_reserved_gb': 1.28125, 'cpu_rss_gb': 2.0319175720214844, 'cpu_vms_gb': 5.079742431640625, 'cpu_percent': 6.385778950421093, 'system_total_gb': 31.819416046142578, 'system_available_gb': 12.171836853027344, 'system_used_percent': 61.7}
2025-05-30 22:08:05,422 - INFO - 📊 Benchmarking training speed...
2025-05-30 22:08:05,422 - INFO - 🔥 Warming up...
2025-05-30 22:08:05,422 - INFO - ✅ Warmup completed in 0.00s
2025-05-30 22:08:05,424 - INFO - ⏱️ Starting speed benchmark...
2025-05-30 22:08:05,424 - INFO - Starting FastLLaMA training...
2025-05-30 22:08:05,424 - INFO - Starting foundation phase...
2025-05-30 22:08:14,698 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 22:08:14,698 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 22:11:54,102 - ERROR - ❌ Benchmark failed: CalledProcessError: Command '['D:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\runtime\\tcc\\tcc.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33m5ws1u\\__triton_launcher.c', '-O3', '-shared', '-fPIC', '-Wno-psabi', '-o', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33m5ws1u\\__triton_launcher.cp312-win_amd64.pyd', '-lcuda', '-lpython3', '-LD:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\lib', '-LC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\lib\\x64', '-LD:\\.conda\\envs\\tts\\libs', '-ID:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\include', '-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\include', '-IC:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33m5ws1u', '-ID:\\.conda\\envs\\tts\\Include']' returned non-zero exit status 1.
2025-05-30 22:11:54,102 - ERROR - ❌ Speed optimization failed: CalledProcessError: Command '['D:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\runtime\\tcc\\tcc.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33m5ws1u\\__triton_launcher.c', '-O3', '-shared', '-fPIC', '-Wno-psabi', '-o', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33m5ws1u\\__triton_launcher.cp312-win_amd64.pyd', '-lcuda', '-lpython3', '-LD:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\lib', '-LC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\lib\\x64', '-LD:\\.conda\\envs\\tts\\libs', '-ID:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\include', '-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\include', '-IC:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33m5ws1u', '-ID:\\.conda\\envs\\tts\\Include']' returned non-zero exit status 1.
2025-05-30 22:12:58,493 - INFO - 🚀 FastLLaMA Training Speed Optimization
2025-05-30 22:12:58,493 - INFO - 🎯 Target: Increase speed from 1.1 it/s to 3-5 it/s
2025-05-30 22:12:58,493 - INFO - Using device: cuda
2025-05-30 22:12:58,493 - INFO - GPU: NVIDIA GeForce RTX 4070
2025-05-30 22:12:58,493 - INFO - GPU Memory: 12.9GB
2025-05-30 22:12:58,493 - INFO - 📚 Creating speed-optimized data configuration...
2025-05-30 22:12:58,493 - INFO - ✅ Batch size increased: 2 → 8
2025-05-30 22:12:58,493 - INFO - ✅ Num workers increased: 2 → 4
2025-05-30 22:12:58,629 - INFO - ✅ Tokenizer loaded. Vocab size: 64811
2025-05-30 22:12:58,629 - INFO - 🧠 Creating speed-optimized FastLLaMA model...
2025-05-30 22:13:01,463 - INFO - 🚀 Applying speed optimizations...
2025-05-30 22:13:01,463 - INFO - 📦 Compiling model with torch.compile...
2025-05-30 22:13:02,058 - INFO - ✅ Model compiled successfully
2025-05-30 22:13:02,061 - INFO - ✅ Speed optimizations applied
2025-05-30 22:13:02,437 - INFO - ✅ Model created with 333.3M total parameters
2025-05-30 22:13:02,437 - INFO - ✅ Trainable parameters: 333.3M
2025-05-30 22:13:02,437 - INFO - ⚙️ Setting up speed-optimized training configuration...
2025-05-30 22:13:02,437 - INFO - ✅ Effective batch size: 4
2025-05-30 22:13:02,437 - INFO - 🏋️ Creating optimized trainer...
2025-05-30 22:13:02,452 - INFO - Initial memory usage: {'gpu_allocated_gb': 1.2524456977844238, 'gpu_reserved_gb': 1.28125, 'gpu_max_allocated_gb': 1.2524456977844238, 'gpu_max_reserved_gb': 1.28125, 'cpu_rss_gb': 2.0318679809570312, 'cpu_vms_gb': 5.080623626708984, 'cpu_percent': 6.385623098835441, 'system_total_gb': 31.819416046142578, 'system_available_gb': 13.608875274658203, 'system_used_percent': 57.2}
2025-05-30 22:13:02,452 - INFO - 📊 Benchmarking training speed...
2025-05-30 22:13:02,452 - INFO - 🔥 Warming up...
2025-05-30 22:13:02,452 - INFO - ✅ Warmup completed in 0.00s
2025-05-30 22:13:02,452 - INFO - ⏱️ Starting speed benchmark...
2025-05-30 22:13:02,452 - INFO - Starting FastLLaMA training...
2025-05-30 22:13:02,452 - INFO - Starting foundation phase...
2025-05-30 22:13:10,052 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 22:13:10,053 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 22:16:48,094 - ERROR - ❌ Benchmark failed: CalledProcessError: Command '['D:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\runtime\\tcc\\tcc.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7xx3_hb0\\__triton_launcher.c', '-O3', '-shared', '-fPIC', '-Wno-psabi', '-o', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7xx3_hb0\\__triton_launcher.cp312-win_amd64.pyd', '-lcuda', '-lpython3', '-LD:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\lib', '-LC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\lib\\x64', '-LD:\\.conda\\envs\\tts\\libs', '-ID:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\include', '-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\include', '-IC:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7xx3_hb0', '-ID:\\.conda\\envs\\tts\\Include']' returned non-zero exit status 1.
2025-05-30 22:16:48,094 - ERROR - ❌ Speed optimization failed: CalledProcessError: Command '['D:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\runtime\\tcc\\tcc.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7xx3_hb0\\__triton_launcher.c', '-O3', '-shared', '-fPIC', '-Wno-psabi', '-o', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7xx3_hb0\\__triton_launcher.cp312-win_amd64.pyd', '-lcuda', '-lpython3', '-LD:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\lib', '-LC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\lib\\x64', '-LD:\\.conda\\envs\\tts\\libs', '-ID:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\include', '-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\include', '-IC:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp7xx3_hb0', '-ID:\\.conda\\envs\\tts\\Include']' returned non-zero exit status 1.
2025-05-30 22:19:03,346 - INFO - 🚀 FastLLaMA Training Speed Optimization
2025-05-30 22:19:03,346 - INFO - 🎯 Target: Increase speed from 1.1 it/s to 3-5 it/s
2025-05-30 22:19:03,346 - INFO - Using device: cuda
2025-05-30 22:19:03,346 - INFO - GPU: NVIDIA GeForce RTX 4070
2025-05-30 22:19:03,346 - INFO - GPU Memory: 12.9GB
2025-05-30 22:19:03,346 - INFO - 📚 Creating speed-optimized data configuration...
2025-05-30 22:19:03,346 - INFO - ✅ Batch size increased: 2 → 8
2025-05-30 22:19:03,346 - INFO - ✅ Num workers increased: 2 → 1
2025-05-30 22:19:03,479 - INFO - ✅ Tokenizer loaded. Vocab size: 64811
2025-05-30 22:19:03,479 - INFO - 🧠 Creating speed-optimized FastLLaMA model...
2025-05-30 22:19:06,334 - INFO - 🚀 Applying speed optimizations...
2025-05-30 22:19:06,334 - INFO - 📦 Compiling model with torch.compile...
2025-05-30 22:19:06,925 - INFO - ✅ Model compiled successfully
2025-05-30 22:19:06,927 - INFO - ✅ Speed optimizations applied
2025-05-30 22:19:07,350 - INFO - ✅ Model created with 333.3M total parameters
2025-05-30 22:19:07,350 - INFO - ✅ Trainable parameters: 333.3M
2025-05-30 22:19:07,350 - INFO - ⚙️ Setting up speed-optimized training configuration...
2025-05-30 22:19:07,350 - INFO - ✅ Effective batch size: 4
2025-05-30 22:19:07,350 - INFO - 🏋️ Creating optimized trainer...
2025-05-30 22:19:07,361 - INFO - Initial memory usage: {'gpu_allocated_gb': 1.2524456977844238, 'gpu_reserved_gb': 1.28125, 'gpu_max_allocated_gb': 1.2524456977844238, 'gpu_max_reserved_gb': 1.28125, 'cpu_rss_gb': 2.031993865966797, 'cpu_vms_gb': 5.0797576904296875, 'cpu_percent': 6.386018722091327, 'system_total_gb': 31.819416046142578, 'system_available_gb': 13.746456146240234, 'system_used_percent': 56.8}
2025-05-30 22:19:07,361 - INFO - 📊 Benchmarking training speed...
2025-05-30 22:19:07,361 - INFO - 🔥 Warming up...
2025-05-30 22:19:07,361 - INFO - ✅ Warmup completed in 0.00s
2025-05-30 22:19:07,361 - INFO - ⏱️ Starting speed benchmark...
2025-05-30 22:19:07,361 - INFO - Starting FastLLaMA training...
2025-05-30 22:19:07,361 - INFO - Starting foundation phase...
2025-05-30 22:19:15,834 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 22:19:15,834 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 22:22:27,451 - ERROR - ❌ Benchmark failed: CalledProcessError: Command '['D:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\runtime\\tcc\\tcc.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjxp77d_\\__triton_launcher.c', '-O3', '-shared', '-fPIC', '-Wno-psabi', '-o', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjxp77d_\\__triton_launcher.cp312-win_amd64.pyd', '-lcuda', '-lpython3', '-LD:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\lib', '-LC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\lib\\x64', '-LD:\\.conda\\envs\\tts\\libs', '-ID:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\include', '-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\include', '-IC:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjxp77d_', '-ID:\\.conda\\envs\\tts\\Include']' returned non-zero exit status 1.
2025-05-30 22:22:27,451 - ERROR - ❌ Speed optimization failed: CalledProcessError: Command '['D:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\runtime\\tcc\\tcc.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjxp77d_\\__triton_launcher.c', '-O3', '-shared', '-fPIC', '-Wno-psabi', '-o', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjxp77d_\\__triton_launcher.cp312-win_amd64.pyd', '-lcuda', '-lpython3', '-LD:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\lib', '-LC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\lib\\x64', '-LD:\\.conda\\envs\\tts\\libs', '-ID:\\.conda\\envs\\tts\\Lib\\site-packages\\triton\\backends\\nvidia\\include', '-IC:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\include', '-IC:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjxp77d_', '-ID:\\.conda\\envs\\tts\\Include']' returned non-zero exit status 1.
2025-05-30 22:23:24,948 - INFO - 🚀 FastLLaMA Training Speed Optimization
2025-05-30 22:23:24,948 - INFO - 🎯 Target: Increase speed from 1.1 it/s to 3-5 it/s
2025-05-30 22:23:24,951 - INFO - Using device: cuda
2025-05-30 22:23:24,951 - INFO - GPU: NVIDIA GeForce RTX 4070
2025-05-30 22:23:24,951 - INFO - GPU Memory: 12.9GB
2025-05-30 22:23:24,951 - INFO - 📚 Creating speed-optimized data configuration...
2025-05-30 22:23:24,951 - INFO - ✅ Batch size increased: 2 → 8
2025-05-30 22:23:24,951 - INFO - ✅ Num workers increased: 2 → 1
2025-05-30 22:23:25,130 - INFO - ✅ Tokenizer loaded. Vocab size: 64811
2025-05-30 22:23:25,130 - INFO - 🧠 Creating speed-optimized FastLLaMA model...
2025-05-30 22:23:28,033 - INFO - 🚀 Applying speed optimizations...
2025-05-30 22:23:28,033 - INFO - 📦 Compiling model with torch.compile...
2025-05-30 22:23:28,043 - WARNING - ⚠️ Model compilation failed: No module named 'triton.language'
2025-05-30 22:23:28,045 - INFO - ✅ Speed optimizations applied
2025-05-30 22:23:28,443 - INFO - ✅ Model created with 333.3M total parameters
2025-05-30 22:23:28,443 - INFO - ✅ Trainable parameters: 333.3M
2025-05-30 22:23:28,443 - INFO - ⚙️ Setting up speed-optimized training configuration...
2025-05-30 22:23:28,443 - INFO - ✅ Effective batch size: 4
2025-05-30 22:23:28,443 - INFO - 🏋️ Creating optimized trainer...
2025-05-30 22:23:28,454 - INFO - Initial memory usage: {'gpu_allocated_gb': 1.2524456977844238, 'gpu_reserved_gb': 1.28125, 'gpu_max_allocated_gb': 1.2524456977844238, 'gpu_max_reserved_gb': 1.28125, 'cpu_rss_gb': 1.9815902709960938, 'cpu_vms_gb': 5.030525207519531, 'cpu_percent': 6.227613568151321, 'system_total_gb': 31.819416046142578, 'system_available_gb': 12.662891387939453, 'system_used_percent': 60.2}
2025-05-30 22:23:28,454 - INFO - 📊 Benchmarking training speed...
2025-05-30 22:23:28,454 - INFO - 🔥 Warming up...
2025-05-30 22:23:28,454 - INFO - ✅ Warmup completed in 0.00s
2025-05-30 22:23:28,454 - INFO - ⏱️ Starting speed benchmark...
2025-05-30 22:23:28,454 - INFO - Starting FastLLaMA training...
2025-05-30 22:23:28,456 - INFO - Starting foundation phase...
2025-05-30 22:23:36,093 - INFO - Loaded dataset: HuggingFaceTB/smollm-corpus
2025-05-30 22:23:36,093 - INFO - Initialized StreamingTextDataset with HuggingFaceTB/smollm-corpus
2025-05-30 22:23:54,239 - INFO - Step 10: loss=10.6509, lr=0.0000080, seq_len=512, memory=3.55GB
2025-05-30 22:24:00,695 - INFO - Step 20: loss=9.8742, lr=0.0000160, seq_len=512, memory=3.55GB
2025-05-30 22:24:06,894 - INFO - Step 30: loss=9.6680, lr=0.0000240, seq_len=512, memory=3.55GB
2025-05-30 22:24:13,399 - INFO - Step 40: loss=9.4568, lr=0.0000320, seq_len=512, memory=3.55GB
2025-05-30 22:24:19,954 - INFO - Step 50: loss=8.9671, lr=0.0000400, seq_len=512, memory=3.55GB
2025-05-30 22:24:26,050 - INFO - Step 60: loss=8.5816, lr=0.0000400, seq_len=512, memory=3.55GB
2025-05-30 22:24:32,048 - INFO - Step 70: loss=7.9719, lr=0.0000398, seq_len=512, memory=3.55GB
2025-05-30 22:24:38,050 - INFO - Step 80: loss=8.1170, lr=0.0000396, seq_len=512, memory=3.55GB
2025-05-30 22:24:44,311 - INFO - Step 90: loss=7.9717, lr=0.0000392, seq_len=512, memory=3.55GB
2025-05-30 22:24:50,414 - INFO - Step 100: loss=8.0561, lr=0.0000388, seq_len=512, memory=3.55GB
2025-05-30 22:24:56,568 - INFO - Step 110: loss=8.0110, lr=0.0000383, seq_len=512, memory=3.55GB
2025-05-30 22:25:02,550 - INFO - Step 120: loss=8.0432, lr=0.0000377, seq_len=512, memory=3.55GB
2025-05-30 22:25:08,724 - INFO - Step 130: loss=8.0558, lr=0.0000370, seq_len=512, memory=3.55GB
2025-05-30 22:25:14,750 - INFO - Step 140: loss=7.8713, lr=0.0000362, seq_len=512, memory=3.55GB
2025-05-30 22:25:20,788 - INFO - Step 150: loss=7.8987, lr=0.0000353, seq_len=512, memory=3.55GB
2025-05-30 22:25:26,970 - INFO - Step 160: loss=7.8617, lr=0.0000344, seq_len=512, memory=3.55GB
2025-05-30 22:25:33,120 - INFO - Step 170: loss=7.7302, lr=0.0000334, seq_len=512, memory=3.55GB
2025-05-30 22:25:39,298 - INFO - Step 180: loss=7.6645, lr=0.0000323, seq_len=512, memory=3.55GB
2025-05-30 22:25:45,330 - INFO - Step 190: loss=7.7678, lr=0.0000312, seq_len=512, memory=3.55GB
2025-05-30 22:25:51,403 - INFO - Step 200: loss=8.1282, lr=0.0000300, seq_len=512, memory=3.55GB
2025-05-30 22:25:57,522 - INFO - Step 210: loss=8.0146, lr=0.0000288, seq_len=512, memory=3.55GB
2025-05-30 22:26:03,512 - INFO - Step 220: loss=7.7615, lr=0.0000275, seq_len=512, memory=3.55GB
2025-05-30 22:26:09,547 - INFO - Step 230: loss=8.1040, lr=0.0000262, seq_len=512, memory=3.55GB
2025-05-30 22:26:15,650 - INFO - Step 240: loss=8.0067, lr=0.0000248, seq_len=512, memory=3.55GB
2025-05-30 22:26:21,527 - INFO - Step 250: loss=7.8006, lr=0.0000235, seq_len=512, memory=3.55GB
2025-05-30 22:26:27,436 - INFO - Step 260: loss=7.3869, lr=0.0000221, seq_len=512, memory=3.55GB
2025-05-30 22:26:33,348 - INFO - Step 270: loss=8.0131, lr=0.0000207, seq_len=512, memory=3.55GB
2025-05-30 22:26:39,172 - INFO - Step 280: loss=7.8047, lr=0.0000193, seq_len=512, memory=3.55GB
2025-05-30 22:26:45,186 - INFO - Step 290: loss=8.0489, lr=0.0000179, seq_len=512, memory=3.55GB
2025-05-30 22:26:51,101 - INFO - Step 300: loss=8.4442, lr=0.0000165, seq_len=512, memory=3.55GB
