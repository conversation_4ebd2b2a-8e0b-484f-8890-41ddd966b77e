{"device": "cuda", "hidden_size": 768, "intermediate_size": 3072, "num_attention_heads": 8, "num_key_value_heads": 4, "num_hidden_layers": 8, "vocab_size": 64819, "max_position_embeddings": 4096, "rope_theta": 10000.0, "rope_scaling": {"type": "linear", "factor": 1.0}, "hidden_act": "silu", "rms_norm_eps": 1e-06, "local_attention_window": 512, "sparse_attention_stride": 4, "compression_ratio": 8, "local_layers": [1, 2], "sparse_layers": [3, 4], "hierarchical_layers": [5, 6], "full_attention_layers": [7, 8], "early_exit_layers": [2, 4, 6], "confidence_threshold": 0.85, "enable_early_exit": false, "compression_encoder_layers": 2, "enable_context_compression": true, "progressive_compression": true, "use_gradient_checkpointing": true, "gradient_checkpointing_ratio": 0.5, "use_mixed_precision": true, "kv_cache_quantization": true, "parameter_sharing": true, "initializer_range": 0.02, "use_cache": true, "pad_token_id": null, "bos_token_id": 1, "eos_token_id": 2, "use_flash_attention": true, "use_kernel_fusion": true, "enable_speculative_decoding": true, "max_batch_size": 32, "dynamic_batching": true, "memory_aware_batching": true, "output_attentions": false, "output_hidden_states": false, "use_return_dict": true}