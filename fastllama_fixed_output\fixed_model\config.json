{"device": "cuda", "hidden_size": 768, "intermediate_size": 3072, "num_attention_heads": 8, "num_key_value_heads": 8, "num_hidden_layers": 8, "vocab_size": 64819, "max_position_embeddings": 4096, "rope_theta": 10000.0, "rope_scaling": {"type": "linear", "factor": 1.0}, "hidden_act": "silu", "rms_norm_eps": 1e-06, "local_attention_window": 512, "sparse_attention_stride": 4, "compression_ratio": 8, "local_layers": [], "sparse_layers": [], "hierarchical_layers": [], "full_attention_layers": [1, 2, 3, 4, 5, 6, 7, 8], "early_exit_layers": [], "confidence_threshold": 0.85, "enable_early_exit": true, "compression_encoder_layers": 0, "enable_context_compression": false, "progressive_compression": false, "use_gradient_checkpointing": true, "gradient_checkpointing_ratio": 0.5, "use_mixed_precision": true, "kv_cache_quantization": false, "parameter_sharing": false, "initializer_range": 0.02, "use_cache": true, "pad_token_id": null, "bos_token_id": 1, "eos_token_id": 2, "use_flash_attention": true, "use_kernel_fusion": true, "enable_speculative_decoding": true, "max_batch_size": 32, "dynamic_batching": true, "memory_aware_batching": true, "output_attentions": false, "output_hidden_states": false, "use_return_dict": true}